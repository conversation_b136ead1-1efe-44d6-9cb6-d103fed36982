<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>#(title) - Wellup</title>
    <link href="https://fonts.cdnfonts.com/css/graphik" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Graphik', sans-serif;
            background: linear-gradient(135deg, #1470C4 0%, #0A5A9E 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 100%;
            max-width: 400px;
        }
        
        .logo {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .logo h1 {
            color: #1470C4;
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 8px;
        }
        
        .logo p {
            color: #646F79;
            font-size: 14px;
        }
        
        .form-title {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .form-title h2 {
            color: #001018;
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .form-title p {
            color: #646F79;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            color: #001018;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #E5E7EB;
            border-radius: 8px;
            font-size: 16px;
            font-family: 'Graphik', sans-serif;
            transition: border-color 0.2s ease;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #1470C4;
        }
        
        .form-group small {
            color: #6B7280;
            font-size: 12px;
            margin-top: 4px;
            display: block;
        }
        
        .password-requirements {
            background: #F8FAFC;
            border: 1px solid #E2E8F0;
            border-radius: 6px;
            padding: 12px;
            margin-top: 8px;
        }
        
        .password-requirements h5 {
            color: #374151;
            font-size: 12px;
            font-weight: 600;
            margin-bottom: 6px;
        }
        
        .password-requirements ul {
            list-style: none;
            padding: 0;
        }
        
        .password-requirements li {
            color: #6B7280;
            font-size: 11px;
            margin-bottom: 2px;
            padding-left: 16px;
            position: relative;
        }
        
        .password-requirements li:before {
            content: "•";
            color: #9CA3AF;
            position: absolute;
            left: 0;
        }
        
        .btn {
            width: 100%;
            padding: 14px;
            background: #1470C4;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            font-family: 'Graphik', sans-serif;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }
        
        .btn:hover {
            background: #0A5A9E;
        }
        
        .btn:disabled {
            background: #9CA3AF;
            cursor: not-allowed;
        }
        
        .alert {
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
        }
        
        .alert-error {
            background: #FEF2F2;
            color: #DC2626;
            border: 1px solid #FECACA;
        }
        
        .alert-success {
            background: #F0FDF4;
            color: #16A34A;
            border: 1px solid #BBF7D0;
        }
        
        .back-link {
            text-align: center;
            margin-top: 20px;
        }
        
        .back-link a {
            color: #1470C4;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
        }
        
        .back-link a:hover {
            text-decoration: underline;
        }
        
        .token-info {
            background: #FEF3C7;
            border: 1px solid #F59E0B;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 20px;
        }
        
        .token-info p {
            color: #92400E;
            font-size: 12px;
            margin: 0;
        }
    </style>
    <script>
        function validateForm() {
            const password = document.getElementById('newPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            const submitBtn = document.getElementById('submitBtn');
            
            if (password !== confirmPassword) {
                document.getElementById('passwordError').textContent = 'Passwords do not match';
                submitBtn.disabled = true;
                return false;
            } else if (password.length < 8) {
                document.getElementById('passwordError').textContent = 'Password must be at least 8 characters long';
                submitBtn.disabled = true;
                return false;
            } else {
                document.getElementById('passwordError').textContent = '';
                submitBtn.disabled = false;
                return true;
            }
        }
        
        window.onload = function() {
            const urlParams = new URLSearchParams(window.location.search);
            const token = urlParams.get('token');
            if (token) {
                document.getElementById('token').value = token;
            }
        }
    </script>
</head>
<body>
    <div class="container">
        <div class="logo">
            <h1>Wellup</h1>
            <p>Healthcare Platform</p>
        </div>
        
        <div class="form-title">
            <h2>Reset Password</h2>
            <p>Enter your reset token and choose a new password for your account.</p>
        </div>
        
        #if(error):
            <div class="alert alert-error">
                #(error)
            </div>
        #endif
        
        #if(success):
            <div class="alert alert-success">
                #(success)
            </div>
        #endif
        
        <div class="token-info">
            <p>⏰ Reset tokens expire after 30 minutes for security. If your token has expired, please request a new one.</p>
        </div>
        
        <form method="POST" action="/password-reset/reset" onsubmit="return validateForm()">
            <div class="form-group">
                <label for="token">Reset Token</label>
                <input 
                    type="text" 
                    id="token" 
                    name="token" 
                    required 
                    placeholder="Enter the reset token from your email"
                    autocomplete="off"
                >
                <small>Check your email for the reset token we sent you.</small>
            </div>
            
            <div class="form-group">
                <label for="newPassword">New Password</label>
                <input 
                    type="password" 
                    id="newPassword" 
                    name="newPassword" 
                    required 
                    placeholder="Enter your new password"
                    autocomplete="new-password"
                    oninput="validateForm()"
                >
                <div class="password-requirements">
                    <h5>Password Requirements:</h5>
                    <ul>
                        <li>At least 8 characters long</li>
                        <li>Mix of letters, numbers, and symbols recommended</li>
                        <li>Avoid common passwords</li>
                    </ul>
                </div>
            </div>
            
            <div class="form-group">
                <label for="confirmPassword">Confirm New Password</label>
                <input 
                    type="password" 
                    id="confirmPassword" 
                    name="confirmPassword" 
                    required 
                    placeholder="Confirm your new password"
                    autocomplete="new-password"
                    oninput="validateForm()"
                >
                <small id="passwordError" style="color: #DC2626;"></small>
            </div>
            
            <button type="submit" class="btn" id="submitBtn">Reset Password</button>
        </form>
        
        <div class="back-link">
            <a href="/password-reset/forgot">← Request New Token</a>
        </div>
    </div>
</body>
</html>
