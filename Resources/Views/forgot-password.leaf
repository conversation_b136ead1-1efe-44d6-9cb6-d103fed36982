<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>#(title) - Wellup</title>
    <link href="https://fonts.cdnfonts.com/css/graphik" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Graphik', sans-serif;
            background: #F5F5F5;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 100%;
            max-width: 400px;
        }
        
        .logo {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .logo h1 {
            color: #1470C4;
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 8px;
        }
        
        .logo p {
            color: #646F79;
            font-size: 14px;
        }
        
        .form-title {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .form-title h2 {
            color: #001018;
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .form-title p {
            color: #646F79;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            color: #001018;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #E5E7EB;
            border-radius: 8px;
            font-size: 16px;
            font-family: 'Graphik', sans-serif;
            transition: border-color 0.2s ease;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #1470C4;
        }
        
        .btn {
            width: 100%;
            padding: 14px;
            background: linear-gradient(to bottom, #FD8205, #E97100);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            font-family: 'Graphik', sans-serif;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .btn:hover {
            background: linear-gradient(to bottom, #E97100, #D66600);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(253, 130, 5, 0.3);
        }
        
        .btn:disabled {
            background: #9CA3AF;
            cursor: not-allowed;
        }
        
        .alert {
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
        }
        
        .alert-error {
            background: #FEF2F2;
            color: #DC2626;
            border: 1px solid #FECACA;
        }
        
        .alert-success {
            background: #F0FDF4;
            color: #16A34A;
            border: 1px solid #BBF7D0;
        }
        
        .back-link {
            text-align: center;
            margin-top: 20px;
        }
        
        .back-link a {
            color: #1470C4;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
        }
        
        .back-link a:hover {
            text-decoration: underline;
        }
        
        .security-note {
            background: #F8FAFC;
            border: 1px solid #E2E8F0;
            border-radius: 8px;
            padding: 16px;
            margin-top: 20px;
        }
        
        .security-note h4 {
            color: #374151;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .security-note p {
            color: #6B7280;
            font-size: 12px;
            line-height: 1.4;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">
            <h1>Wellup</h1>
            <p>Healthcare Platform</p>
        </div>
        
        <div class="form-title">
            <h2>Forgot Password</h2>
            <p>Enter your email address and we'll send you a link to reset your password.</p>
        </div>
        
        #if(error):
            <div class="alert alert-error">
                #(error)
            </div>
        #endif
        
        #if(success):
            <div class="alert alert-success">
                #(success)
            </div>
        #endif
        
        <form method="POST" action="/password-reset/forgot">
            <div class="form-group">
                <label for="username">Email Address</label>
                <input 
                    type="email" 
                    id="username" 
                    name="username" 
                    required 
                    placeholder="Enter your email address"
                    autocomplete="email"
                >
            </div>
            
            <button type="submit" class="btn">Send Reset Link</button>
        </form>
        
        <div class="back-link">
            <a href="#(loginUrl)">← Back to Login</a>
        </div>
        
        <div class="security-note">
            <h4>🔒 Security Notice</h4>
            <p>For your security, we'll only send reset instructions to the email address associated with your account. If you don't receive an email within a few minutes, please check your spam folder.</p>
        </div>
    </div>
</body>
</html>
