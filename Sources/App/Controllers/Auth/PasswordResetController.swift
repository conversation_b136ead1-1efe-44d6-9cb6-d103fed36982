//
//  PasswordResetController.swift
//  
//
//  Created by <PERSON> on 6/29/25.
//

import Foundation
import Vapor
import Fluent

// MARK: - Request/Response Models
struct ForgotPasswordRequest: Content {
    let username: String
}

struct ResetPasswordRequest: Content {
    let token: String
    let newPassword: String
    let confirmPassword: String
}

struct PasswordResetResponse: Content {
    let success: Bool
    let message: String
}

// MARK: - Password Reset Controller
struct PasswordResetController: RouteCollection {
    
    func boot(routes: RoutesBuilder) throws {
        let passwordReset = routes.grouped("password-reset")

        // Apply rate limiting middleware to password reset routes
        let rateLimitedRoutes = passwordReset.grouped(PasswordResetRateLimitMiddleware())

        // Web routes for Leaf templates
        passwordReset.get("forgot", use: showForgotPasswordPage)
        passwordReset.get("verify", use: showVerifyTokenPage)

        // API routes for form submissions (with rate limiting)
        rateLimitedRoutes.post("forgot", use: handleForgotPassword)
        passwordReset.post("reset", use: handlePasswordReset)
    }
    
    // MARK: - Web Routes (Leaf Templates)
    
    func showForgotPasswordPage(req: Request) async throws -> View {
        var context: [String: String] = [
            "title": "Forgot Password",
            "loginUrl": loginUrl()
        ]

        // Only add error/success if they exist and are not empty
        if let error: String = req.query["error"], !error.isEmpty {
            context["error"] = error
        }
        if let success: String = req.query["success"], !success.isEmpty {
            context["success"] = success
        }

        return try await req.view.render("forgot-password", context)
    }
    
    func showVerifyTokenPage(req: Request) async throws -> View {
        var context: [String: String] = [
            "title": "Reset Password",
            "loginUrl": loginUrl()
        ]

        // Pre-fill token if provided in URL
        if let token: String = req.query["token"], !token.isEmpty {
            context["token"] = token
        }

        // Only add error/success if they exist and are not empty
        if let error: String = req.query["error"], !error.isEmpty {
            context["error"] = error
        }
        if let success: String = req.query["success"], !success.isEmpty {
            context["success"] = success
        }

        return try await req.view.render("verify-reset-token", context)
    }
    
    // MARK: - API Routes
    
    func handleForgotPassword(req: Request) async throws -> Response {
        let input = try req.content.decode(ForgotPasswordRequest.self)
        
        // Find user by username (email)
        guard let user = try await AuthUser.query(on: req.db)
            .filter(\.$username == input.username.lowercased())
            .first() else {
            // Log failed attempt for security monitoring
            await logSecurityEvent(req: req, event: "Password reset requested for non-existent user", username: input.username, success: false)
            // Don't reveal if user exists or not for security
            return req.redirect(to: "/password-reset/forgot?success=If an account with that email exists, you will receive a reset link.")
        }
        
        // Generate and set reset token
        user.setResetToken(expirationMinutes: 30)
        
        try await user.save(on: req.db)
        
        // Send reset token via SMS/Email
        try await sendResetToken(req: req, user: user)
        
        // Log the password reset attempt
        let cloudwatch = CloudWatchLogger(req: req, logGroupName: .actions)
        let logMessage = CloudWatchLogMessage.send(msg: .security(msg: "Password reset requested for user: \(user.username)"))
        _ = try await cloudwatch.putLog(message: logMessage, on: req.eventLoop).get()
        
        return req.redirect(to: "/password-reset/forgot?success=If an account with that email exists, you will receive a reset link.")
    }
    
    func handlePasswordReset(req: Request) async throws -> Response {
        let input = try req.content.decode(ResetPasswordRequest.self)

        // Validate passwords match
        guard input.newPassword == input.confirmPassword else {
            return req.redirect(to: "/password-reset/verify?error=Passwords do not match.")
        }

        // Enhanced password validation
        guard isPasswordValid(input.newPassword) else {
            return req.redirect(to: "/password-reset/verify?error=Password must be at least 8 characters long and contain a mix of letters, numbers, and symbols.")
        }

        // Validate token format (basic check)
        guard !input.token.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            return req.redirect(to: "/password-reset/verify?error=Reset token is required.")
        }
        
        // Find user by reset token
        guard let user = try await AuthUser.query(on: req.db)
            .filter(\.$resetToken == input.token)
            .first() else {
            await logSecurityEvent(req: req, event: "Password reset attempted with invalid token", success: false)
            return req.redirect(to: "/password-reset/verify?error=Invalid or expired reset token.")
        }

        // Validate token is still valid
        guard user.isResetTokenValid() else {
            await logSecurityEvent(req: req, event: "Password reset attempted with expired token", username: user.username, success: false)
            return req.redirect(to: "/password-reset/verify?error=Reset token has expired. Please request a new one.")
        }
        
        // Update password and clear reset token
        user.passwordHash = try Bcrypt.hash(input.newPassword)
        user.clearResetToken()
        
        try await user.save(on: req.db)
        
        // Log successful password reset
        let cloudwatch = CloudWatchLogger(req: req, logGroupName: .actions)
        let logMessage = CloudWatchLogMessage.send(msg: .security(msg: "Password successfully reset for user: \(user.username)"))
        _ = try await cloudwatch.putLog(message: logMessage, on: req.eventLoop).get()
        
        return req.redirect(to: "\(loginUrl())?success=Password reset successfully. Please log in with your new password.")
    }
    
    // MARK: - Helper Methods
    
    private func sendResetToken(req: Request, user: AuthUser) async throws {
        guard let token = user.resetToken else {
            throw Abort(.internalServerError, reason: "Reset token not generated")
        }
        
        // Try to find associated User or Member to get contact info
        let userRecord = try? await User.query(on: req.db)
            .filter(\.$auth == user.id?.uuidString)
            .first()
        
        let memberRecord = try? await Member.query(on: req.db)
            .filter(\.$auth == user.id?.uuidString)
            .first()
        
        let email = userRecord?.email ?? memberRecord?.email ?? user.username
        
        // Send via email (primary method)
        try await sendResetTokenEmail(req: req, email: email, token: token, username: user.username)
        
        // If we have phone number, also send SMS
        // Note: You'll need to add phone field to User/Member models if not already present
        // For now, we'll just use email
    }
    
    private func sendResetTokenEmail(req: Request, email: String, token: String, username: String) async throws {
        let resetLink = "https://\(req.headers.first(name: .host) ?? "localhost")/password-reset/verify?token=\(token)"
        
        let emailData: [String: Any] = [
            "personalizations": [
                [
                    "to": [["email": email]],
                    "dynamic_template_data": [
                        "username": username,
                        "reset_link": resetLink,
                        "token": token
                    ]
                ]
            ],
            "from": ["email": "<EMAIL>", "name": "Wellup"],
            "template_id": "d-e1fb2b4866d541f59ce341503ade89c2" // You'll need to create this template in SendGrid
        ]
        
        _ = try await TwilioController.fireEmail(req: req, input: emailData).get()
    }

    // MARK: - Security Helper Methods

    private func isPasswordValid(_ password: String) -> Bool {
        // Basic password validation
        guard password.count >= 8 else { return false }

        let hasLetter = password.rangeOfCharacter(from: .letters) != nil
        let hasNumber = password.rangeOfCharacter(from: .decimalDigits) != nil
        let hasSpecialChar = password.rangeOfCharacter(from: CharacterSet(charactersIn: "!@#$%^&*()_+-=[]{}|;:,.<>?")) != nil

        return hasLetter && (hasNumber || hasSpecialChar)
    }

    private func logSecurityEvent(req: Request, event: String, username: String? = nil, success: Bool = true) async {
        let clientIP = req.remoteAddress?.hostname ?? "unknown"
        let userAgent = req.headers.first(name: "User-Agent") ?? "unknown"

        let logMessage = """
        Password Reset Security Event: \(event)
        IP: \(clientIP)
        User-Agent: \(userAgent)
        Username: \(username ?? "unknown")
        Success: \(success)
        Timestamp: \(Date())
        """

        let cloudwatch = CloudWatchLogger(req: req, logGroupName: .actions)
        let cloudwatchMessage = CloudWatchLogMessage.send(msg: .security(msg: logMessage))
        _ = try? await cloudwatch.putLog(message: cloudwatchMessage, on: req.eventLoop).get()
    }

    private func loginUrl() -> String {
        return isProduction ? "https://dona-workspace-prod.duploapps.dona.health/login" : "https://dona-workspace-stag01.duploapps.dona.health/login"
    }
}
