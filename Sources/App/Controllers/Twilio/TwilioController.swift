//
//  File.swift
//
//
//  Created by <PERSON> on 3/27/23.
//


import Foundation
import Vapor
import Fluent



struct TwilioAuthHeader {
    static func basicAuth() -> String {
        guard let token = Environment.get("TWILIO_TOKEN") else { return "" }
        guard let accountSID = Environment.get("TWILIO_ACCOUNT_SID") else { return "" }
        let loginString = String(format: "%@:%@", accountSID, token)
        let loginData = loginString.data(using: String.Encoding.utf8)!
        return loginData.base64EncodedString()
    }
}

//struct EmailTemplate {
//    static let navigatorInvite = "d-2856c3b55cdb45c3ba20aa11c7f5b493"
//    static let memberInvite = "d-1f959abe91744ba2a91e08b304da2b5d"
//    static let schedulerInvite = "d-e7a3e5950cc546f7a0dddbb7dc69b0cf"
//}
enum EmailTempaltes: String, Codable {
    case scheduler, navigator, member, passwordReset

    func tempalte() -> String {
        switch self {
        case .scheduler:
            "d-e7a3e5950cc546f7a0dddbb7dc69b0cf"
        case .navigator:
            "d-2856c3b55cdb45c3ba20aa11c7f5b493"
        case .member:
            "d-1f959abe91744ba2a91e08b304da2b5d"
        case .passwordReset:
            "d-e1fb2b4866d541f59ce341503ade89c2" // You'll need to create this in SendGrid
        }
    }
}

struct EmailInput: Content {
    var template: EmailTempaltes
}

struct SMSInput: Content {
    var to: String
    var body: String

    func dataEncode() -> Data? {
        return "Body=\(body)&From=+13058719378&To=\(to)".data(using: .utf8)
    }
}

struct PasswordResetEmailData: Content {
    let username: String
    let resetToken: String
    let resetLink: String
    let expirationMinutes: Int

    func json() -> [String: Any] {
        return [
            "personalizations": [
                [
                    "to": [["email": username]],
                    "dynamic_template_data": [
                        "username": username,
                        "reset_token": resetToken,
                        "reset_link": resetLink,
                        "expiration_minutes": expirationMinutes
                    ]
                ]
            ],
            "from": ["email": "<EMAIL>", "name": "Wellup"],
            "template_id": EmailTempaltes.passwordReset.tempalte()
        ]
    }
}

struct NotificationConfigurationInput: Content {
    var addedToConversationEnabled: Bool
    var addedToConversationSound: String
    var addedToConversationTemplate: String
    var newMessageBadgeCountEnabled: Bool
    var newMessageEnabled: Bool
    var logEnabled: Bool
    
    func dataEncode() -> Data? {
        return "AddedToConversation.Sound=\(addedToConversationSound)&AddedToConversation.Enabled=\(addedToConversationEnabled)&AddedToConversation.Template=\(addedToConversationTemplate)&NewMessage.BadgeCountEnabled=\(newMessageBadgeCountEnabled)&NewMessage.Enabled=\(newMessageEnabled)&LogEnabled=\(logEnabled)".data(using: .utf8)
    }
}

struct AddConversationMessageInput: Content {
    var author:String
    var body:String
    var sendTo: String? = nil //navigator | member
    var memberMessage: Bool? = nil
    var attributes:[String:String]
    
    func dataEncode() -> Data? {
        guard let theJSONData = try? JSONSerialization.data(withJSONObject: attributes, options: []) else { return nil }
        guard let theJSONText = String(data: theJSONData, encoding: .ascii) else { return nil }
        let name = author.replacingOccurrences(of: " ", with: "-")
        //        print("JSON string = \(theJSONText)")
        return "Attributes=\(theJSONText)&Body=\(body)&Author=\(name.lowercased())".data(using: .utf8)
    }
}

struct AddMemberInput: Content {
    var identity:String
    var attributes:[String:String]?
    var userID:String?
    
    func dataEncode() -> Data? {
        let dataString = "Identity=\(identity)"
        if let attr = attributes {
            guard let theJSONData = try? JSONSerialization.data(withJSONObject: attr, options: []) else { return nil }
            guard let theJSONText = String(data: theJSONData, encoding: .ascii) else { return nil }
            return "\(dataString)&Attributes=\(theJSONText)".data(using: .utf8)
        } else {
            return dataString.data(using: .utf8)
        }
        
    }
}

struct CreateMemberConversationInput: Content {
    var creatorID:    String
    var orgID:        String
    var role:         String
    var FriendlyName: String
    var participants: [String]
    var message:AddConversationMessageInput
    
    func toJSON() -> [String:Any?]  {
        return [ "FriendlyName" : self.FriendlyName ]
    }
    
    func dataEncode() -> Data? {
        "FriendlyName=\(FriendlyName)".data(using: .utf8)
    }
}

struct UpdateConversationInput: Content {
    var title: String
}

struct CreateConversationInput: Content {
    var creatorID:    String
    var orgID:        String
    var FriendlyName: String
    var participants: [String]
    var message:AddConversationMessageInput
    
    func toJSON() -> [String:Any?]  {
        return [ "FriendlyName" : self.FriendlyName ]
    }
    
    func dataEncode() -> Data? {
        "FriendlyName=\(FriendlyName)".data(using: .utf8)
    }
}

//MARK: - Models

struct Conversation: Content {
    var unique_name:               String?//          null,
    var date_updated:              String?//          "2023-03-28T23:04:09Z",
    var friendly_name:             String?//          "House Visit",
    var account_sid:               String?//          "AC41b48b0ee97d3596a8e8164ede6b5678",
    var url:                       String?//          "https://conversations.twilio.com/v1/Conversations/CH00fd075ceb96428abe76e06add91997c",
    var state:                     String?//          "active",
    var date_created:              String?//          "2023-03-28T23:04:09Z",
    var messaging_service_sid:     String?//          "MGf3952acef95bbd76bfc3b11e2fa5a444",
    var sid:                       String?//          "CH00fd075ceb96428abe76e06add91997c",
    var attributes:                String?//          "{}",
    var bindings:                  String?//          null,
    var chat_service_sid:          String?//          "ISc434c656410a4a9b873358a1fb06ea8a",
    
    
    func chat(creator:User) -> Chat {
        return Chat(creatorID: creator.id?.uuidString ?? "", chatServiceSid: chat_service_sid ?? "", conversationSid: sid ?? "", conversationFriendlyName: friendly_name ?? "", conversationState: state ?? "", pushEnabled: false, latestMessage: nil)
    }
    
    func chat(creatorID:String) -> Chat {
        return Chat(creatorID: creatorID, chatServiceSid: "", conversationSid: sid ?? "", conversationFriendlyName: friendly_name ?? "", conversationState: state ?? "", pushEnabled: false, latestMessage: nil)
    }
    
    func memberChat(createId: UUID) -> MemberChat {
        return MemberChat(creatorID: createId,
                          chatServiceSid: "",
                          conversationSid: "",
                          conversationFriendlyName: "",
                          conversationState: "",
                          pushEnabled: false,
                          latestMessage: "")
    }
}

struct ContactController: RouteCollection {
    func boot(routes: RoutesBuilder) throws {
        let contact = routes.grouped("contact")
        contact.post("sms", use: sendSms)
        contact.post("email", use: email)
    }
    
    func sendSms(req:Request)  throws -> EventLoopFuture<ClientResponse> {
        let input = try req.content.decode(SMSInput.self)
        let url = queryURL(urlString: "https://api.twilio.com/2010-04-01/Accounts/AC41b48b0ee97d3596a8e8164ede6b5678/Messages.json")
        let uri = URI(string: url!)
        guard let data = input.dataEncode() else { throw Abort(.badRequest) }
        return req.client.post(uri, headers: ["content-type":"application/x-www-form-urlencoded",
                                              "Authorization":"Basic \(TwilioAuthHeader.basicAuth())"], beforeSend: { req in
            
            req.body = ByteBuffer.init(data: data)
            //            print(req)
        }).flatMap { response in
            print(response)
            return req.eventLoop.future(response)
        }
    }
    
    static func emailScheduler(req: Request, invite: SchedulerEmailInvite) throws -> EventLoopFuture<ClientResponse> {
        let email = invite.json()
        guard let key = Environment.get("SENDGRID_API_KEY") else { throw Abort(.notFound, reason: "inbox id is required") }
        let client = req.client
        let url = queryURL(urlString: "https://api.sendgrid.com/v3/mail/send")
        let uri = URI(string: url!)
        guard let data = try? JSONSerialization.data(withJSONObject: email) else { throw Abort(.badRequest) }
        return client.post(uri, headers: [
            "Authorization":key,
            "content-type":"application/json"], beforeSend: { req in
                
                req.body = ByteBuffer.init(data: data)
            }).flatMap { response in
                print(response)
                return req.eventLoop.future(response)
            }
    }
    
    func email(req: Request) throws -> EventLoopFuture<ClientResponse> {
        let input = try req.content.decode(EmailInput.self)
        let emailData = input.template == .scheduler ? SchedulerEmailInvite(first: "Anthony",
                                                                            email: "anthony@misfitlabs.",
                                                                            org: "Wellup!",
                                                                            url: "https://wellup-web-stg-38c16252b3d5.herokuapp.com/",
                                                                            tempId: input.template.tempalte()).json() : [:]
        guard let key = Environment.get("SENDGRID_API_KEY") else { throw Abort(.notFound, reason: "inbox id is required") }
        let client = req.client
        let url = queryURL(urlString: "https://api.sendgrid.com/v3/mail/send")
        let uri = URI(string: url!)
        guard let data = try? JSONSerialization.data(withJSONObject: emailData) else { throw Abort(.badRequest) }
        return client.post(uri, headers: [
            "Authorization":key,
            "content-type":"application/json"], beforeSend: { req in
                
                req.body = ByteBuffer.init(data: data)
            }).flatMap { response in
                print(response)
                return req.eventLoop.future(response)
            }
    }
}


struct TwilioController: RouteCollection {
    
    func boot(routes: RoutesBuilder) throws {
        let networks = routes.grouped("conversations")
        
        networks.get(":chatID", use: getChat)
        
        networks.get([":chatID",  "messages"], use: getMessages)
        networks.post([":chatID", "messages"], use: createMessage)
        
        networks.post(use: create)
        networks.post("sms", use: sendSms)
        
        
        networks.put([":chatID", "add"], use: addMember)
        
        networks.delete(":chatID", use: deleteConvo)
    }
    
    func getMembersConvo(req: Request) throws -> EventLoopFuture<ClientResponse> {
        //needs to be memberid
        guard let userID = req.parameters.get("userID") else { throw NetworkError.error(type: .chat) }
        guard let id = userID.addingPercentEncoding(withAllowedCharacters: .urlHostAllowed) else { throw NetworkError.error(type: .chat) }
        
        //        let page:String      = req.query["page"] ?? "0"
        let pageSize:String  = req.query["size"] ?? "50"
        let url = queryURL(urlString: "https://conversations.twilio.com/v1/ParticipantConversations?Identity=\(id)&PageSize=\(pageSize)")
        let uri = URI(string: url!)
        return req.client.get(uri, headers: ["Authorization":"Basic \(TwilioAuthHeader.basicAuth())"]).flatMap { response in
            //            print(response)
            return req.eventLoop.future(response)
        }
    }
    
    static func fetchParticipants(req: Request, convoID:String) throws -> EventLoopFuture<ClientResponse> {
        let url = queryURL(urlString: "https://conversations.twilio.com/v1/Conversations/\(convoID)/Participants")
        let uri = URI(string: url!)
        return req.client.get(uri, headers: ["content-type":"application/x-www-form-urlencoded",
                                             "Authorization":"Basic \(TwilioAuthHeader.basicAuth())"], beforeSend: { req in
        }).flatMap { response in
            return req.eventLoop.future(response)
        }
    }
    
    static func createMessage(req: Request, convoID:String, input:AddConversationMessageInput) throws -> EventLoopFuture<ClientResponse> {
        let url = queryURL(urlString: "https://conversations.twilio.com/v1/Conversations/\(convoID)/Messages")
        let uri = URI(string: url!)
        guard let data = input.dataEncode() else { throw Abort(.badRequest) }
        return req.client.post(uri, headers: ["content-type":"application/x-www-form-urlencoded",
                                              "Authorization":"Basic \(TwilioAuthHeader.basicAuth())"], beforeSend: { req in
            
            req.body = ByteBuffer.init(data: data)
            //            print(req)
        }).flatMap { response in
            return req.eventLoop.future(response)
        }
    }
    
    func createMessage(req: Request) throws -> EventLoopFuture<ClientResponse> {
        guard let id = req.parameters.get("chatID") else { throw NetworkError.error(type: .chat) }
        let input = try req.content.decode(AddConversationMessageInput.self)
        let url = queryURL(urlString: "https://conversations.twilio.com/v1/Conversations/\(id)/Messages")
        let uri = URI(string: url!)
        guard let data = input.dataEncode() else { throw Abort(.badRequest) }
        return req.client.post(uri, headers: ["content-type":"application/x-www-form-urlencoded",
                                              "Authorization":"Basic \(TwilioAuthHeader.basicAuth())"], beforeSend: { req in
            
            req.body = ByteBuffer.init(data: data)
            //            print(req)
        }).flatMap { response in
            return req.eventLoop.future(response)
        }
    }
    
    func getMessages(req: Request) throws -> EventLoopFuture<ClientResponse> {
        guard let id = req.parameters.get("chatID") else { throw NetworkError.error(type: .chat) }
        let page:String      = req.query["page"] ?? "0"
        let pageSize:String  = req.query["size"] ?? "50"
        var urlString = "https://conversations.twilio.com/v1/Conversations/\(id)/Messages?Page=\(page)&PageSize=\(pageSize)"
        if let pageToken:String  = req.query["PageToken"] {
            urlString = "\(urlString)&PageToken=\(pageToken)"
        }
        let url = queryURL(urlString: urlString)
        let uri = URI(string: url!)
        return req.client.get(uri, headers: ["Authorization":"Basic \(TwilioAuthHeader.basicAuth())"]).flatMap { response in
            //            print(response)
            return req.eventLoop.future(response)
        }
    }
    
    func getChat(req: Request) throws -> EventLoopFuture<ClientResponse> {
        guard let id = req.parameters.get("chatID") else { throw NetworkError.error(type: .chat) }
        let url = queryURL(urlString: "https://conversations.twilio.com/v1/Conversations/\(id)")
        let uri = URI(string: url!)
        return req.client.get(uri, headers: ["Authorization":"Basic \(TwilioAuthHeader.basicAuth())"]).flatMap { response in
            //            print(response)
            return req.eventLoop.future(response)
        }
    }
    
    func deleteConvo(req: Request, id:String) throws -> EventLoopFuture<ClientResponse> {
        let url = queryURL(urlString: "https://conversations.twilio.com/v1/Conversations/\(id)")
        let uri = URI(string: url!)
        return req.client.delete(uri, headers: ["Authorization":"Basic \(TwilioAuthHeader.basicAuth())"]).flatMap { response in
            //            print(response)
            return req.eventLoop.future(response)
        }
    }
    
    func deleteConvo(req: Request) throws -> EventLoopFuture<ClientResponse> {
        guard let id = req.parameters.get("chatID") else { throw NetworkError.error(type: .chat) }
        let url = queryURL(urlString: "https://conversations.twilio.com/v1/Conversations/\(id)")
        let uri = URI(string: url!)
        return req.client.delete(uri, headers: ["Authorization":"Basic \(TwilioAuthHeader.basicAuth())"]).flatMap { response in
            //            print(response)
            return req.eventLoop.future(response)
        }
    }
    
    
    static func addMember(req: Request, id:String, input:AddMemberInput) throws -> EventLoopFuture<ClientResponse> {
        
        //        guard let id = req.parameters.get("chatID") else { throw NetworkError.error(type: .chat) }//CHXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
        //        let input = try req.content.decode(AddMemberInput.self)
        let url = queryURL(urlString: "https://conversations.twilio.com/v1/Conversations/\(id)/Participants")
        let uri = URI(string: url!)
        guard let data = input.dataEncode() else { throw Abort(.badRequest) }
        return req.client.post(uri, headers: ["content-type":"application/x-www-form-urlencoded",
                                              "Authorization":"Basic \(TwilioAuthHeader.basicAuth())"], beforeSend: { req in
            
            req.body = ByteBuffer.init(data: data)
            //            print(req)
        }).flatMap { response in
            return req.eventLoop.future(response)
        }
    }
    
    func addMember(req: Request) throws -> EventLoopFuture<ClientResponse> {
        guard let id = req.parameters.get("chatID") else { throw NetworkError.error(type: .chat) }//CHXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
        let input = try req.content.decode(AddMemberInput.self)
        let url = queryURL(urlString: "https://conversations.twilio.com/v1/Conversations/\(id)/Participants")
        let uri = URI(string: url!)
        guard let data = input.dataEncode() else { throw Abort(.badRequest) }
        return req.client.post(uri, headers: ["content-type":"application/x-www-form-urlencoded",
                                              "Authorization":"Basic \(TwilioAuthHeader.basicAuth())"], beforeSend: { req in
            
            req.body = ByteBuffer.init(data: data)
            //            print(req)
        }).flatMap { response in
            
            return req.eventLoop.future(response)
        }
    }
    
    func create(req: Request) throws -> EventLoopFuture<Conversation> {
        let input = try req.content.decode(CreateConversationInput.self)
        let url = queryURL(urlString: "https://conversations.twilio.com/v1/Conversations")
        let uri = URI(string: url!)
        guard let data = input.dataEncode() else { throw Abort(.badRequest) }
        
        return req.client.post(uri, headers: ["content-type":"application/x-www-form-urlencoded",
                                              "Authorization":"Basic \(TwilioAuthHeader.basicAuth())"], beforeSend: { req in
            req.body = ByteBuffer.init(data: data)
            //            print(req)
        }).flatMap { response in
            print(response)
            if let convo = try? response.content.decode(Conversation.self) {
                return req.eventLoop.future(convo)
            } else {
                return req.eventLoop.makeFailedFuture(Abort(.badRequest))
            }
        }
    }
    
    //    curl -X POST "https://api.twilio.com/2010-04-01/Accounts/$TWILIO_ACCOUNT_SID/Messages.json" \
    //    --data-urlencode "Body=Hi there" \
    //    --data-urlencode "From=+***********" \
    //    --data-urlencode "To=+***********" \
    //    -u $TWILIO_ACCOUNT_SID:$TWILIO_AUTH_TOKEN
    func sendSms(req:Request)  throws -> EventLoopFuture<ClientResponse> {
        let input = try req.content.decode(SMSInput.self)
        let url = queryURL(urlString: "https://api.twilio.com/2010-04-01/Accounts/AC41b48b0ee97d3596a8e8164ede6b5678/Messages.json")
        let uri = URI(string: url!)
        guard let data = input.dataEncode() else { throw Abort(.badRequest) }
        return req.client.post(uri, headers: ["content-type":"application/x-www-form-urlencoded",
                                              "Authorization":"Basic \(TwilioAuthHeader.basicAuth())"], beforeSend: { req in
            
            req.body = ByteBuffer.init(data: data)
            //            print(req)
        }).flatMap { response in
            print(response)
            return req.eventLoop.future(response)
        }
    }
    
    func enablePushNotification(req: Request, chatServiceID:String) throws -> EventLoopFuture<ClientResponse> {
        let input = NotificationConfigurationInput(
            addedToConversationEnabled: true,
            addedToConversationSound: "default",
            addedToConversationTemplate: "There is a new message in ${CONVERSATION} from ${PARTICIPANT}: ${MESSAGE}",
            newMessageBadgeCountEnabled: true,
            newMessageEnabled: true,
            logEnabled: true)
        let url = queryURL(urlString: "https://conversations.twilio.com/v1/Services/\(chatServiceID)/Configuration/Notifications")
        let uri = URI(string: url!)
        guard let data = input.dataEncode() else { throw Abort(.badRequest) }
        return req.client.post(uri, headers: ["content-type":"application/x-www-form-urlencoded",
                                              "Authorization":"Basic \(TwilioAuthHeader.basicAuth())"], beforeSend: { req in
            
            req.body = ByteBuffer.init(data: data)
            //            print(req)
        }).flatMap { response in
            
            return req.eventLoop.future(response)
        }
    }
    
    static func fireEmail(req: Request, input:[String:Any?]) throws -> EventLoopFuture<ClientResponse> {
        guard let key = Environment.get("SENDGRID_API_KEY") else { throw Abort(.notFound, reason: "inbox id is required") }
        let client = req.client
        let url = queryURL(urlString: "https://api.sendgrid.com/v3/mail/send")
        let uri = URI(string: url!)
        guard let data = try? JSONSerialization.data(withJSONObject: input) else { throw Abort(.badRequest) }
        return client.post(uri, headers: [
            "Authorization":key,
            "content-type":"application/json"], beforeSend: { req in
                
                req.body = ByteBuffer.init(data: data)
            }).flatMap { response in
                print(response)
                return req.eventLoop.future(response)
            }
    }
}


extension TwilioController {
    static func consentMessage(phone:String, name:String, url:String) -> SMSInput {
        let msg = "Hi \(name.capitalized)! \n\nWelcome to the Genesis Program. To get started, please click the link below and sign the consent form. We can't wait to have you on board!\n\n\(url)\n\nLet me know if you need any assistance or have any questions. Thank you!"
        return SMSInput(to: phone, body: msg)
    }
    
    static func sendSmsMessage(req:Request, input:SMSInput)  throws -> EventLoopFuture<ClientResponse> {
        let url = queryURL(urlString: "https://api.twilio.com/2010-04-01/Accounts/AC41b48b0ee97d3596a8e8164ede6b5678/Messages.json")
        let uri = URI(string: url!)
        guard let data = input.dataEncode() else { throw Abort(.badRequest) }
        return req.client.post(uri, headers: ["content-type":"application/x-www-form-urlencoded",
                                              "Authorization":"Basic \(TwilioAuthHeader.basicAuth())"], beforeSend: { req in
            req.body = ByteBuffer.init(data: data)
            //            print(req)
        }).flatMap { response in
            return req.eventLoop.future(response)
        }
    }

    // MARK: - Password Reset Methods

    static func sendPasswordResetEmail(req: Request, email: String, resetToken: String, resetLink: String) throws -> EventLoopFuture<ClientResponse> {
        let emailData = PasswordResetEmailData(
            username: email,
            resetToken: resetToken,
            resetLink: resetLink,
            expirationMinutes: 30
        )

        return try fireEmail(req: req, input: emailData.json())
    }

    static func sendPasswordResetSMS(req: Request, phoneNumber: String, resetToken: String) throws -> EventLoopFuture<ClientResponse> {
        let message = "Your Wellup password reset code is: \(resetToken). This code expires in 30 minutes. If you didn't request this, please ignore this message."
        let smsInput = SMSInput(to: phoneNumber, body: message)

        return try sendSmsMessage(req: req, input: smsInput)
    }
}




struct EmailTemplate {
    static let navigatorInvite = "d-2856c3b55cdb45c3ba20aa11c7f5b493"
    static let memberInvite = "d-1f959abe91744ba2a91e08b304da2b5d"
    static let schedulerInvite = "d-e7a3e5950cc546f7a0dddbb7dc69b0cf"
}

struct SchedulerEmailInvite {
    var first: String
    var email: String
    var org: String
    var url: String
    var tempId: String
    
    func json() -> [String: Any?] {
        return [
            "from":[
                "email": "<EMAIL>"
            ],
            "personalizations":[
                [
                    "to":[
                        [
                            "email":email
                        ]
                    ],
                    "dynamic_template_data":[
                        "first":first,
                        "email":email,
                        "url": url,
                        "org": org,
                    ]
                ]
            ],
            "template_id": tempId
        ]
    }
}

struct EmailTemplateMember {
    var to: String
    var first: String
    var username: String
    var pwd: String
    var tempID: String
    var iosURL: String
    
    func json() -> [String: Any?] {
        return [
            "from":[
                "email": "<EMAIL>"
            ],
            "personalizations":[
                [
                    "to":[
                        [
                            "email":to
                        ]
                    ],
                    "dynamic_template_data":[
                        "first":first,
                        "email":username,
                        "pwd":pwd,
                        "ios_url": iosURL
                    ]
                ]
            ],
            "template_id": tempID
        ]
    }
}

struct EmailTemplateNavigator {
    var to: String
    var first: String
    var username: String
    var pwd: String
    var tempID: String
    var url: String
    var iosURL: String
    
    func json() -> [String: Any?] {
        return [
            "from":[
                "email": "<EMAIL>"
            ],
            "personalizations":[
                [
                    "to":[
                        [
                            "email":to
                        ]
                    ],
                    "dynamic_template_data":[
                        "first":first,
                        "email":username,
                        "pwd":pwd,
                        "url": url,
                        "ios_url": iosURL
                    ]
                ]
            ],
            "template_id": tempID
        ]
    }
}


struct TwilioService {
    static func createConversation(req: Request,
                                   org: Organization,
                                   creatorId: UUID,
                                   message: AddConversationMessageInput,
                                   chatMembers:[ChatMember],
                                   chatMemberCreator: ChatMember) throws -> EventLoopFuture<MemberChat> {
        //create conversation
        return try! TwilioController().create(req: req).flatMap { conversation in
            
            let chat = conversation.memberChat(createId: creatorId)
            let convoID = conversation.sid ?? ""
            
            return chatMembers.sequencedFlatMapEach(on: req.eventLoop) { member in
                let memberData = member.memberInfo()
                
                return try! TwilioController.addMember(req: req,
                                                       id: convoID,
                                                       input: AddMemberInput(identity: memberData.identity,
                                                                             attributes: ["fullName": memberData.fullName])).flatMap { res in
                    return req.eventLoop.future(chat)
                }
            }.flatMap { _ in
                return try! createTwilioMessageUpdateMemberChat(req: req,
                                                                chat: chat,
                                                                org: org,
                                                                convoID: convoID,
                                                                message: message,
                                                                chatMembers: chatMembers,
                                                                chatMemberCreator: chatMemberCreator, 
                                                                twilioConversation: conversation)
            }
        }
    }
    
    static func createTwilioMessageUpdateMemberChat(req: Request,
                                                    chat:MemberChat,
                                                    org: Organization,
                                                    convoID: String,
                                                    message: AddConversationMessageInput,
                                                    chatMembers:[ChatMember],
                                                    chatMemberCreator: ChatMember,
                                                    twilioConversation: Conversation) throws -> EventLoopFuture<MemberChat> {
        return try! TwilioController.createMessage(req: req, convoID: convoID, input: message).flatMap { msgResponse in
            
            return org.$memberChats.create(chat, on: req.db).transform(to: chat).flatMap { newChat in
                
                newChat.$creator.id = chatMemberCreator.id
                newChat.$latestMessageSender.id = chatMemberCreator.id
                newChat.latestMessage = message.body
                newChat.chatServiceSid = twilioConversation.chat_service_sid ?? ""
                newChat.conversationSid = twilioConversation.sid ?? ""
                newChat.conversationFriendlyName = twilioConversation.friendly_name ?? ""
                newChat.conversationState = twilioConversation.state ?? ""
                
                
                return newChat.update(on: req.db).transform(to: newChat).flatMap { updatedChat in
                    return updatedChat.$participants.attach(chatMembers, on: req.db).transform(to: updatedChat)
                    
                }
            }
        }
    }
}
